﻿using Keleid.BLL.DTOs;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoryService> _logger;

        public CategoryService(ApplicationDbContext context, ILogger<CategoryService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<CategoryWithSubCategoriesDto>> GetMainCategoriesWithSubCategoriesAsync()
        {
            try
            {
                var categories = await _context.Categories
                    .Where(c => c.ParentCategoryId == null)
                    .Include(c => c.SubCategories)
                    .OrderBy(c => c.Id)
                    .ToListAsync();

                return categories.Select(c => new CategoryWithSubCategoriesDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Slug = c.Slug,
                    IconClass = GetIconClassForCategory(c.Title),
                    SubCategories = c.SubCategories.Select(sc => new SubCategoryDto
                    {
                        Id = sc.Id,
                        Title = sc.Title,
                        Slug = sc.Slug,
                        ParentCategoryId = sc.ParentCategoryId.Value
                    }).ToList()
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting main categories with subcategories");
                return new List<CategoryWithSubCategoriesDto>();
            }
        }

        public async Task<List<CategoryDto>> GetMainCategoriesAsync()
        {
            try
            {
                var categories = await _context.Categories
                    .Where(c => c.ParentCategoryId == null)
                    .OrderBy(c => c.Id)
                    .ToListAsync();

                return categories.Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Slug = c.Slug,
                    ParentCategoryId = c.ParentCategoryId
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting main categories");
                return new List<CategoryDto>();
            }
        }

        public async Task<List<CategoryDto>> GetSubCategoriesAsync(int parentCategoryId)
        {
            try
            {
                var subCategories = await _context.Categories
                    .Where(c => c.ParentCategoryId == parentCategoryId)
                    .OrderBy(c => c.Id)
                    .ToListAsync();

                return subCategories.Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Slug = c.Slug,
                    ParentCategoryId = c.ParentCategoryId
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subcategories for parent {ParentCategoryId}", parentCategoryId);
                return new List<CategoryDto>();
            }
        }

        public async Task<CategoryDto?> GetCategoryByIdAsync(int categoryId)
        {
            try
            {
                var category = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Id == categoryId);

                if (category == null)
                    return null;

                return new CategoryDto
                {
                    Id = category.Id,
                    Title = category.Title,
                    Slug = category.Slug,
                    ParentCategoryId = category.ParentCategoryId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category by id {CategoryId}", categoryId);
                return null;
            }
        }

        public async Task<List<CategoryDto>> GetAllCategoriesAsync()
        {
            try
            {
                var categories = await _context.Categories
                    .OrderBy(c => c.ParentCategoryId ?? 0)
                    .ThenBy(c => c.Id)
                    .ToListAsync();

                return categories.Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    Slug = c.Slug,
                    ParentCategoryId = c.ParentCategoryId
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all categories");
                return new List<CategoryDto>();
            }
        }

        public async Task<CategoryDto?> GetCategoryBySlugAsync(string slug)
        {
            try
            {
                var category = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Slug == slug);

                if (category == null)
                    return null;

                return new CategoryDto
                {
                    Id = category.Id,
                    Title = category.Title,
                    Slug = category.Slug,
                    ParentCategoryId = category.ParentCategoryId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category by slug {Slug}", slug);
                return null;
            }
        }

        public async Task<List<CategoryFeatureDto>> GetCategoryFeaturesAsync(int categoryId)
        {
            try
            {
                var features = await _context.CategoryFeatures
                    .Where(cf => cf.CategoryId == categoryId)
                    .OrderBy(cf => cf.Id)
                    .ToListAsync();

                return features.Select(f => new CategoryFeatureDto
                {
                    Id = f.Id,
                    CategoryId = f.CategoryId,
                    Title = f.Title,
                    InputType = f.InputType,
                    IsRequired = f.IsRequired,
                    Options = f.Options
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category features for category {CategoryId}", categoryId);
                return new List<CategoryFeatureDto>();
            }
        }

        public async Task<bool> CreateMainCategoryAsync(string title, string slug)
        {
            try
            {
                // بررسی تکراری نبودن عنوان و slug
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Title == title || c.Slug == slug);

                if (existingCategory != null)
                {
                    _logger.LogWarning("Category with title '{Title}' or slug '{Slug}' already exists", title, slug);
                    return false;
                }

                var category = new Category
                {
                    Title = title,
                    Slug = slug,
                    ParentCategoryId = null
                };

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Main category '{Title}' created successfully with ID {Id}", title, category.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating main category '{Title}'", title);
                return false;
            }
        }

        public async Task<bool> CreateSubCategoryAsync(int parentId, string title, string slug)
        {
            try
            {
                // بررسی وجود دسته والد
                var parentCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Id == parentId && c.ParentCategoryId == null);

                if (parentCategory == null)
                {
                    _logger.LogWarning("Parent category with ID {ParentId} not found", parentId);
                    return false;
                }

                // بررسی تکراری نبودن عنوان و slug
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Title == title || c.Slug == slug);

                if (existingCategory != null)
                {
                    _logger.LogWarning("Category with title '{Title}' or slug '{Slug}' already exists", title, slug);
                    return false;
                }

                var category = new Category
                {
                    Title = title,
                    Slug = slug,
                    ParentCategoryId = parentId
                };

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Sub category '{Title}' created successfully under parent {ParentId} with ID {Id}",
                    title, parentId, category.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sub category '{Title}' under parent {ParentId}", title, parentId);
                return false;
            }
        }

        public async Task<bool> UpdateCategoryTitleAsync(int id, string title, string slug)
        {
            try
            {
                var category = await _context.Categories.FirstOrDefaultAsync(c => c.Id == id);
                if (category == null)
                {
                    _logger.LogWarning("Category with ID {Id} not found", id);
                    return false;
                }

                // بررسی تکراری نبودن عنوان و slug (به جز خود این دسته)
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Id != id && (c.Title == title || c.Slug == slug));

                if (existingCategory != null)
                {
                    _logger.LogWarning("Another category with title '{Title}' or slug '{Slug}' already exists", title, slug);
                    return false;
                }

                category.Title = title;
                category.Slug = slug;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Category {Id} title updated to '{Title}'", id, title);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category {Id} title to '{Title}'", id, title);
                return false;
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                var category = await _context.Categories
                    .Include(c => c.SubCategories)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (category == null)
                {
                    _logger.LogWarning("Category with ID {Id} not found", id);
                    return false;
                }

                // بررسی وجود زیردسته‌ها
                if (category.SubCategories.Any())
                {
                    _logger.LogWarning("Cannot delete category {Id} because it has subcategories", id);
                    return false;
                }

                // بررسی وجود آگهی در این دسته
                var hasAdvertisements = await _context.Advertisements
                    .AnyAsync(a => a.CategoryId == id);

                if (hasAdvertisements)
                {
                    _logger.LogWarning("Cannot delete category {Id} because it has advertisements", id);
                    return false;
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Category {Id} deleted successfully", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category {Id}", id);
                return false;
            }
        }

        /// <summary>
        /// تعیین آیکون مناسب برای هر دسته‌بندی بر اساس عنوان
        /// </summary>
        /// <param name="categoryTitle">عنوان دسته‌بندی</param>
        /// <returns>کلاس آیکون</returns>
        private string GetIconClassForCategory(string categoryTitle)
        {
            return categoryTitle switch
            {
                "جایگاه سوخت" => "fas fa-gas-pump",
                "زمین با کاربری صنعتی" => "fas fa-industry",
                "سایت پرورش آبزیان" => "fas fa-fish",
                "سردخانه، انبار، سیلو" => "fas fa-warehouse",
                "سوله" => "fas fa-industry",
                "کارخانه صنعتی و تولیدی" => "fas fa-industry",
                "مجتمع کشت و صنعت" => "fas fa-tractor",
                "ماشین‌آلات خطوط تولید" => "fas fa-cogs",
                "مراکز درمانی" => "fas fa-hospital",
                "نیروگاه" => "fas fa-bolt",
                "محل اقامت" => "fas fa-hotel",
                "واحد پرورش طیور" => "fas fa-dove",
                "واحد دامداری" => "fas fa-cow",
                "واحد کشاورزی" => "fas fa-seedling",
                "واحد مرغداری" => "fas fa-kiwi-bird",
                _ => "fas fa-folder"
            };
        }
    }
}
