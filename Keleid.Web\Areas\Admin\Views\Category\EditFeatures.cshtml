﻿@model List<Keleid.BLL.DTOs.CategoryFeatureDto>
@{
    ViewBag.Title = "ویرایش ویژگی های دسته بندی";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <div class="row clearfix">
            <div class="col-lg-10 col-md-12 mx-auto">
                <div class="card">
                    <div class="header">
                        <h2><i class="fas fa-list text-info"></i> ویرایش <strong>ویژگی های دسته بندی</strong></h2>
                        <ul class="header-dropdown m-r--5">
                            <li>
                                <button type="button" class="btn btn-info btn-sm" onclick="addNewFeature()">
                                    <i class="fas fa-plus"></i> افزودن ویژگی جدید
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="body">
                        <!-- نمایش اطلاعات دسته -->
                        <div class="alert alert-info">
                            <i class="fas fa-folder-open text-info"></i>
                            <strong>دسته بندی:</strong> @ViewBag.CategoryTitle
                            <br>
                            <small><strong>دسته والد:</strong> @ViewBag.ParentTitle</small>
                        </div>

                        <form asp-area="Admin" asp-controller="Category" asp-action="EditFeatures" method="post">
                            <input type="hidden" name="CategoryId" value="@ViewBag.CategoryId" />

                            <div id="features-container">
                                <!-- ویژگی‌های موجود از دیتابیس -->
                                @if (Model != null && Model.Any())
                                {
                                    @for (int i = 0; i < Model.Count; i++)
                                    {
                                        <div class="feature-item border rounded p-3 mb-3" data-index="@i">
                                            <input type="hidden" name="Features[@i].Id" value="@Model[i].Id" />
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">عنوان ویژگی</label>
                                                        <input type="text" name="Features[@i].Title" class="form-control"
                                                               value="@Model[i].Title" placeholder="مثال: متراژ" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label class="form-label">نوع ورودی</label>
                                                        <select name="Features[@i].InputType" class="form-control" required>
                                                            <option value="">انتخاب کنید</option>
                                                            <option value="text" selected="@(Model[i].InputType == "text")">متن</option>
                                                            <option value="number" selected="@(Model[i].InputType == "number")">عدد</option>
                                                            <option value="select" selected="@(Model[i].InputType == "select")">انتخابی</option>
                                                            <option value="textarea" selected="@(Model[i].InputType == "textarea")">متن طولانی</option>
                                                            <option value="checkbox" selected="@(Model[i].InputType == "checkbox")">چک باکس</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">گزینه‌ها (برای انتخابی)</label>
                                                        <input type="text" name="Features[@i].Options" class="form-control"
                                                               value="@Model[i].Options" placeholder="گزینه1|گزینه2|گزینه3">
                                                        <small class="text-muted">با علامت | جدا کنید</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label class="form-label">اجباری</label>
                                                        <div class="form-check mt-2">
                                                            <input type="checkbox" name="Features[@i].IsRequired"
                                                                   class="form-check-input" value="true" @(Model[i].IsRequired ? "checked" : "")>
                                                            <label class="form-check-label">بله</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label class="form-label">عملیات</label>
                                                        <button type="button" class="btn btn-danger btn-sm d-block"
                                                                onclick="removeFeature(this)">
                                                            <i class="fas fa-trash"></i> حذف
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <!-- نمونه ویژگی اگر هیچ ویژگی موجود نباشد -->
                                    <div class="feature-item border rounded p-3 mb-3" data-index="0">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">عنوان ویژگی</label>
                                                    <input type="text" name="Features[0].Title" class="form-control"
                                                           placeholder="مثال: متراژ" required>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="form-label">نوع ورودی</label>
                                                    <select name="Features[0].InputType" class="form-control" required>
                                                        <option value="">انتخاب کنید</option>
                                                        <option value="text">متن</option>
                                                        <option value="number">عدد</option>
                                                        <option value="select">انتخابی</option>
                                                        <option value="textarea">متن طولانی</option>
                                                        <option value="checkbox">چک باکس</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">گزینه‌ها (برای انتخابی)</label>
                                                    <input type="text" name="Features[0].Options" class="form-control"
                                                           placeholder="گزینه1|گزینه2|گزینه3">
                                                    <small class="text-muted">با علامت | جدا کنید</small>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="form-label">اجباری</label>
                                                    <div class="form-check mt-2">
                                                        <input type="checkbox" name="Features[0].IsRequired"
                                                               class="form-check-input" value="true">
                                                        <label class="form-check-label">بله</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="form-label">عملیات</label>
                                                    <button type="button" class="btn btn-danger btn-sm d-block"
                                                            onclick="removeFeature(this)">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <hr>
                                    <button type="submit" class="btn btn-info btn-round waves-effect">
                                        <i class="fas fa-save"></i> ذخیره ویژگی‌ها
                                    </button>
                                    <a asp-area="Admin" asp-controller="Category" asp-action="Index"
                                       class="btn btn-secondary btn-round waves-effect">
                                        <i class="fas fa-arrow-left"></i> بازگشت
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<script>
// تنظیم شاخص بر اساس تعداد ویژگی‌های موجود
let featureIndex = document.querySelectorAll('.feature-item').length;

function addNewFeature() {
    const container = document.getElementById('features-container');
    const newFeature = `
        <div class="feature-item border rounded p-3 mb-3" data-index="${featureIndex}">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">عنوان ویژگی</label>
                        <input type="text" name="Features[${featureIndex}].Title" class="form-control"
                               placeholder="مثال: متراژ" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">نوع ورودی</label>
                        <select name="Features[${featureIndex}].InputType" class="form-control" required>
                            <option value="">انتخاب کنید</option>
                            <option value="text">متن</option>
                            <option value="number">عدد</option>
                            <option value="select">انتخابی</option>
                            <option value="textarea">متن طولانی</option>
                            <option value="checkbox">چک باکس</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">گزینه‌ها (برای انتخابی)</label>
                        <input type="text" name="Features[${featureIndex}].Options" class="form-control"
                               placeholder="گزینه1|گزینه2|گزینه3">
                        <small class="text-muted">با علامت | جدا کنید</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">اجباری</label>
                        <div class="form-check mt-2">
                            <input type="checkbox" name="Features[${featureIndex}].IsRequired"
                                   class="form-check-input" value="true">
                            <label class="form-check-label">بله</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">عملیات</label>
                        <button type="button" class="btn btn-danger btn-sm d-block"
                                onclick="removeFeature(this)">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', newFeature);
    featureIndex++;
}

function removeFeature(button) {
    const featureItem = button.closest('.feature-item');
    if (confirm('آیا از حذف این ویژگی اطمینان دارید؟')) {
        featureItem.remove();
        reindexFeatures();
    }
}

function reindexFeatures() {
    const features = document.querySelectorAll('.feature-item');
    features.forEach((feature, index) => {
        feature.setAttribute('data-index', index);

        // Update input names
        const inputs = feature.querySelectorAll('input, select');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name && name.includes('Features[')) {
                const newName = name.replace(/Features\[\d+\]/, `Features[${index}]`);
                input.setAttribute('name', newName);
            }
        });
    });

    featureIndex = features.length;
}
</script>